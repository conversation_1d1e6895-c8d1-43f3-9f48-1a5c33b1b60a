# 2. 第一阶段：从DynamicDataSource到ShardingSphere-JDBC的平滑迁移

## 2.1 迁移的必要性

### 2.1.1 分表需求驱动的技术选型

随着业务数据的持续增长，现有的分库架构已无法满足单库内大表的性能要求。为了实现库内分表能力，我们面临一个关键的技术选型问题：

**核心冲突：** 现有的`DynamicDataSource`多数据源管理方案与`ShardingSphere-JDBC`分片中间件存在根本性的架构冲突，两者无法同时使用。

+ **DynamicDataSource的局限性：** 虽然在分库场景下运行稳定，但其设计理念是"多个独立数据源的切换"，无法支持ShardingSphere所需的"逻辑表到物理表的映射"能力。
+ **ShardingSphere的要求：** 作为分片中间件，需要完全接管数据源管理，通过逻辑SQL到物理SQL的转换实现分库分表，与DynamicDataSource的数据源切换机制冲突。

**技术决策：** 为了获得分表能力，必须将现有的DynamicDataSource方案完全替换为ShardingSphere-JDBC，这是一个不可避免的架构升级。

### 2.1.2 现有自研方案的局限性

除了分表能力的硬性要求外，现有自研的动态数据源方案还存在以下问题：

+ **扩展性不足：** 面对复杂的分片场景（如分库分表、读写分离等），自研方案需要大量定制开发，维护成本高。
+ **与主流技术栈脱节：** 团队需要维护大量自研代码，新成员学习成本高，技术债务持续累积。
+ **功能局限性：** 缺乏分布式事务、SQL解析优化、分片算法等企业级特性。

**解决方案：** 采用业界成熟的ShardingSphere-JDBC，获得标准化的分片能力和丰富的企业级特性。

## 2.2 ShardingSphere-JDBC编程式配置

### 2.2.1 多配置源的统一管理

现有系统的数据源配置来自两个不同的源头，迁移方案必须保持这种配置模式的兼容性：

**配置源现状：**
+ **Nacos配置中心：** 存储部分融担的数据库连接信息
+ **APS Dubbo服务：** 通过远程调用获取另一部分融担的配置信息

**统一抽象设计：**

```java
public interface ConfigurationSource {
    /**
     * 获取数据源配置信息
     * @return 数据源配置映射
     */
    Map<String, DataSourceConfiguration> getDataSourceConfigs();
    
    /**
     * 获取分片规则配置
     * @return 分片规则配置
     */
    ShardingRuleConfiguration getShardingRuleConfig();
}

@Component
public class NacosConfigurationSource implements ConfigurationSource {
    
    @Autowired
    private ConfigService configService;
    
    @Override
    public Map<String, DataSourceConfiguration> getDataSourceConfigs() {
        try {
            String config = configService.getConfig("datasource.yaml", "DEFAULT_GROUP", 5000);
            return parseDataSourceConfig(config);
        } catch (Exception e) {
            throw new ConfigurationException("Failed to load config from Nacos", e);
        }
    }
}

@Component
public class ApsConfigurationSource implements ConfigurationSource {
    
    @Reference
    private ApsConfigService apsConfigService;
    
    @Override
    public Map<String, DataSourceConfiguration> getDataSourceConfigs() {
        try {
            List<DatabaseConfig> configs = apsConfigService.getDatabaseConfigs();
            return convertToDataSourceConfig(configs);
        } catch (Exception e) {
            throw new ConfigurationException("Failed to load config from APS", e);
        }
    }
}
```

**配置合并策略：**

```java
@Component
public class CompositeConfigurationSource implements ConfigurationSource {
    
    @Autowired
    private List<ConfigurationSource> configSources;
    
    @Override
    public Map<String, DataSourceConfiguration> getDataSourceConfigs() {
        Map<String, DataSourceConfiguration> mergedConfig = new HashMap<>();
        
        // 按优先级合并配置：APS > Nacos
        for (ConfigurationSource source : configSources) {
            Map<String, DataSourceConfiguration> configs = source.getDataSourceConfigs();
            mergedConfig.putAll(configs);
        }
        
        return mergedConfig;
    }
}
```

### 2.2.2 动态数据源构建

在应用启动时，通过编程方式动态构建ShardingSphere数据源：

```java
@Configuration
public class ShardingSphereDataSourceConfig {
    
    @Autowired
    private CompositeConfigurationSource configSource;
    
    @Bean
    @Primary
    public DataSource shardingSphereDataSource() {
        try {
            // 1. 获取所有数据源配置
            Map<String, DataSourceConfiguration> dataSourceConfigs = configSource.getDataSourceConfigs();
            
            // 2. 创建物理数据源
            Map<String, DataSource> dataSourceMap = createDataSources(dataSourceConfigs);
            
            // 3. 构建分片规则（第一阶段只包含分库规则）
            ShardingRuleConfiguration shardingRuleConfig = buildShardingRules();
            
            // 4. 创建ShardingSphere数据源
            return ShardingSphereDataSourceFactory.createDataSource(
                dataSourceMap, 
                Collections.singleton(shardingRuleConfig), 
                new Properties()
            );
            
        } catch (Exception e) {
            throw new DataSourceInitializationException("Failed to create ShardingSphere DataSource", e);
        }
    }
    
    private Map<String, DataSource> createDataSources(Map<String, DataSourceConfiguration> configs) {
        Map<String, DataSource> dataSourceMap = new HashMap<>();
        
        for (Map.Entry<String, DataSourceConfiguration> entry : configs.entrySet()) {
            DataSourceConfiguration config = entry.getValue();
            HikariDataSource dataSource = new HikariDataSource();
            
            dataSource.setJdbcUrl(config.getUrl());
            dataSource.setUsername(config.getUsername());
            dataSource.setPassword(config.getPassword());
            dataSource.setDriverClassName(config.getDriverClassName());
            
            // 设置连接池参数
            dataSource.setMaximumPoolSize(config.getMaxPoolSize());
            dataSource.setMinimumIdle(config.getMinIdle());
            dataSource.setConnectionTimeout(config.getConnectionTimeout());
            
            dataSourceMap.put(entry.getKey(), dataSource);
        }
        
        return dataSourceMap;
    }
    
    private ShardingRuleConfiguration buildShardingRules() {
        ShardingRuleConfiguration shardingRuleConfig = new ShardingRuleConfiguration();
        
        // 第一阶段：只配置分库规则，不配置分表规则
        ShardingTableRuleConfiguration orderTableRule = new ShardingTableRuleConfiguration(
            "t_order", 
            "db_${0..3}.t_order"  // 只分库，不分表
        );
        
        // 配置分库策略
        orderTableRule.setDatabaseShardingStrategy(
            new StandardShardingStrategyConfiguration("guarantee_no", "guaranteeNoShardingAlgorithm")
        );
        
        shardingRuleConfig.getTables().add(orderTableRule);
        
        // 注册分片算法
        shardingRuleConfig.getShardingAlgorithms().put(
            "guaranteeNoShardingAlgorithm", 
            new AlgorithmConfiguration("HINT", new Properties())
        );
        
        return shardingRuleConfig;
    }
}
```

## 2.3 统一AOP路由机制设计

### 2.3.1 现有上下文路由的保持

现有系统通过AOP切面从ServiceContext获取融担号进行数据源路由，迁移后必须保持这种机制不变：

**现状保持：**
+ SQL语句中不包含融担号条件
+ 通过ServiceContext.getGuaranteeNo()获取路由信息
+ 业务代码无需任何修改

### 2.3.2 统一切面的架构设计

设计一个统一的AOP切面，同时处理分库和分表路由（第一阶段只启用分库）：

```java
@Aspect
@Component
@Order(1)
public class UnifiedShardingAspect {
    
    private static final Logger log = LoggerFactory.getLogger(UnifiedShardingAspect.class);
    
    @Around("execution(* com.example.dao.*.*(..))")
    public Object routeSharding(ProceedingJoinPoint point) throws Throwable {
        // 1. 获取上下文信息
        String guaranteeNo = ServiceContext.getGuaranteeNo();
        
        if (guaranteeNo == null) {
            log.warn("No guarantee number found in context for method: {}", point.getSignature());
            return point.proceed();
        }
        
        // 2. 使用HintManager进行路由
        try (HintManager hintManager = HintManager.getInstance()) {
            // 分库路由（基于融担号）
            hintManager.setDatabaseShardingValue("t_order", "guarantee_no", guaranteeNo);
            
            // 分表路由（第一阶段暂不启用）
            String tableShardingKey = extractTableShardingKey(point);
            if (tableShardingKey != null && isTableShardingEnabled()) {
                hintManager.setTableShardingValue("t_order", "sharding_key", tableShardingKey);
            }
            
            log.debug("Routing with guarantee_no: {}, table_key: {}", guaranteeNo, tableShardingKey);
            
            return point.proceed();
        }
    }
    
    private String extractTableShardingKey(ProceedingJoinPoint point) {
        // 第一阶段返回null，第二阶段实现具体逻辑
        return null;
    }
    
    private boolean isTableShardingEnabled() {
        // 通过配置控制是否启用分表
        return false; // 第一阶段固定返回false
    }
}
```

### 2.3.3 自定义Hint分片算法

实现基于融担号的分库算法：

```java
public class GuaranteeNoHintShardingAlgorithm implements HintShardingAlgorithm<String> {
    
    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames, 
                                       HintShardingValue<String> shardingValue) {
        
        String guaranteeNo = getGuaranteeNoFromHint(shardingValue);
        
        if (guaranteeNo != null) {
            // 根据融担号计算目标数据库
            int dbIndex = Math.abs(guaranteeNo.hashCode()) % availableTargetNames.size();
            
            return availableTargetNames.stream()
                    .filter(name -> name.endsWith("_" + dbIndex))
                    .collect(Collectors.toList());
        }
        
        // 如果没有融担号，抛出异常（避免全库扫描）
        throw new ShardingException("Guarantee number is required for database sharding");
    }
    
    private String getGuaranteeNoFromHint(HintShardingValue<String> shardingValue) {
        // 优先从Hint值获取
        if (!shardingValue.getValues().isEmpty()) {
            return shardingValue.getValues().iterator().next();
        }
        
        // 从上下文获取（兜底方案）
        return ServiceContext.getGuaranteeNo();
    }
}
```

## 2.4 第一阶段实现策略

### 2.4.1 只做分库，不做分表

第一阶段的核心原则是**功能对等**，确保迁移后的分库能力与现有方案完全一致：

**配置约束：**
+ ShardingSphere配置中只包含分库规则
+ 所有表的actualDataNodes配置为：`db_${0..3}.table_name`（不包含分表后缀）
+ 分表相关的算法和策略暂不配置

**代码约束：**
+ 统一AOP中分表逻辑预留但不启用
+ `isTableShardingEnabled()`方法固定返回false
+ `extractTableShardingKey()`方法返回null

### 2.4.2 功能对等性验证

建立完整的验证机制确保迁移成功：

```java
@Component
public class MigrationValidator {
    
    public void validateRouting() {
        // 1. 路由一致性测试
        validateDatabaseRouting();
        
        // 2. 性能对比测试
        validatePerformance();
        
        // 3. 事务一致性测试
        validateTransactionConsistency();
    }
    
    private void validateDatabaseRouting() {
        // 使用相同的融担号，验证路由到相同的数据库
        List<String> testGuaranteeNos = Arrays.asList("GT001", "GT002", "GT003");
        
        for (String guaranteeNo : testGuaranteeNos) {
            ServiceContext.setGuaranteeNo(guaranteeNo);
            
            // 执行测试查询，验证路由结果
            String targetDb = getCurrentTargetDatabase();
            String expectedDb = calculateExpectedDatabase(guaranteeNo);
            
            assert targetDb.equals(expectedDb) : 
                String.format("Routing mismatch for %s: expected %s, actual %s", 
                    guaranteeNo, expectedDb, targetDb);
        }
    }
}
```

## 2.5 为分表阶段奠定基础

### 2.5.1 架构统一的收益

完成第一阶段迁移后，系统将获得以下收益：

+ **技术栈标准化：** 采用业界主流的ShardingSphere-JDBC，降低维护成本
+ **分表能力准备：** 为第二阶段的分表改造提供了技术基础
+ **功能增强：** 获得SQL解析优化、分布式事务等企业级特性

### 2.5.2 平滑过渡的保障

+ **业务逻辑零侵入：** 所有业务代码保持不变，SQL语句无需修改
+ **运维复杂度可控：** 配置管理方式保持一致，运维流程无需调整
+ **风险隔离：** 第一阶段只做技术栈替换，不涉及业务逻辑变更

通过这种分阶段的迁移策略，我们在保证系统稳定性的前提下，为后续的分表改造奠定了坚实的技术基础。第一阶段完成后，系统将具备完整的ShardingSphere分片能力，为第二阶段的库内分表改造做好了充分准备。
